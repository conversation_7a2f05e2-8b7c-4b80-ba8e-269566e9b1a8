#!/usr/bin/env python3
"""
Proposal Generation Script

This script generates a proposal using the same flow as the API endpoint.
You can choose to either:
1. Call the API endpoint directly (simulates frontend behavior)
2. Call the service directly (bypasses API layer)

Usage:
    python generate_proposal_script.py --method api
    python generate_proposal_script.py --method direct
    python generate_proposal_script.py --method queue
"""

import asyncio
import json
import argparse
import httpx
from typing import Optional, Dict, Any

# Direct service imports
from services.proposal.rfp.generate_rfp import RFPGenerationService
from services.proposal.rfi.rfi_generation_service import RFIGenerationService
from services.queue_service.proposal_queue_service import ProposalQueueService
from database import get_customer_db


class ProposalGenerator:
    def __init__(self):
        self.base_url = "http://localhost:8000"  # Adjust if your API runs on different port
        
    async def generate_via_api(self, job_instruction: Dict[str, Any]) -> Dict[str, Any]:
        """Generate proposal by calling the API endpoint directly"""
        print("🌐 Generating proposal via API endpoint...")
        
        # Prepare the request payload for the queue endpoint
        queue_payload = {
            "job_instruction": json.dumps(job_instruction),
            "opps_id": job_instruction["opportunityId"],
            "tenant_id": job_instruction["tenantId"],
            "request_type": 1,  # RFP request type
            "job_submitted_by": "script_user",
            "next_state": None
        }
        
        async with httpx.AsyncClient(timeout=300.0) as client:
            try:
                response = await client.post(
                    f"{self.base_url}/queue/proposal",
                    json=queue_payload
                )
                response.raise_for_status()
                result = response.json()
                print(f"✅ Queue item created: {result}")
                return result
            except httpx.RequestError as e:
                print(f"❌ API request failed: {e}")
                return {"error": str(e)}
            except httpx.HTTPStatusError as e:
                print(f"❌ API returned error {e.response.status_code}: {e.response.text}")
                return {"error": f"HTTP {e.response.status_code}: {e.response.text}"}
    
    async def generate_via_queue(self, job_instruction: Dict[str, Any]) -> Dict[str, Any]:
        """Generate proposal by adding to queue (database direct)"""
        print("📋 Generating proposal via queue service...")
        
        async for db in get_customer_db():
            try:
                new_item = await ProposalQueueService.create_proposal_queue_item(
                    db=db,
                    job_instruction=json.dumps(job_instruction),
                    opps_id=job_instruction["opportunityId"],
                    tenant_id=job_instruction["tenantId"],
                    request_type=1,  # RFP request type
                    job_submitted_by="script_user",
                    next_state=None
                )
                
                if new_item:
                    result = {
                        "message": "Proposal queue item created successfully",
                        "job_id": new_item.job_id,
                        "id": new_item.id
                    }
                    print(f"✅ Queue item created: {result}")
                    return result
                else:
                    print("❌ Failed to create queue item")
                    return {"error": "Failed to create proposal queue item"}
                    
            except Exception as e:
                print(f"❌ Queue creation failed: {e}")
                return {"error": str(e)}
            finally:
                break
    
    async def generate_direct(self, job_instruction: Dict[str, Any]) -> Dict[str, Any]:
        """Generate proposal by calling the service directly"""
        print("🔧 Generating proposal via direct service call...")
        
        try:
            is_rfp = job_instruction.get("isRFP", True)
            
            if is_rfp:
                print("📄 Using RFP Generation Service...")
                rfp_generator = RFPGenerationService()
                await rfp_generator.generate_rfp(
                    job_instruction=json.dumps(job_instruction),
                    job_submitted_by="script_user"
                )
            else:
                print("📋 Using RFI Generation Service...")
                rfi_generator = RFIGenerationService()
                await rfi_generator.generate_rfi(
                    job_instruction=json.dumps(job_instruction),
                    job_submitted_by="script_user"
                )
            
            print("✅ Proposal generation completed successfully!")
            return {"message": "Proposal generated successfully", "method": "direct"}
            
        except Exception as e:
            print(f"❌ Direct generation failed: {e}")
            return {"error": str(e)}


def create_job_instruction(
    opportunity_id: str = "rwpWgMHaAC",
    tenant_id: str = "8d9e9729-f7bd-44a0-9cf1-777f532a2db2",
    client_short_name: str = "adeptengineeringsolutions",
    is_rfp: bool = True,
    volumes: list = None,
    set_for_review: bool = True,
    export_type: int = 1,
    ai_personality_id: str = "6"
) -> Dict[str, Any]:
    """Create a job instruction with the specified parameters"""
    
    if volumes is None:
        volumes = [1, 2, 3, 4, 5]  # Default to all volumes
    
    return {
        "opportunityId": opportunity_id,
        "clientShortName": client_short_name,
        "tenantId": tenant_id,
        "profileId": "2",
        "opportunityType": "custom",
        "sourceDocuments": [],
        "forceRefresh": False,
        "setForReview": set_for_review,
        "exportType": export_type,
        "proposalRequestType": 1,
        "coverPage": None,
        "trailingPage": None,
        "systemPromptParameters": None,
        "isRFP": is_rfp,
        "generatedVolumes": volumes,
        "aiPersonalityId": ai_personality_id
    }


async def main():
    parser = argparse.ArgumentParser(description="Generate a proposal using different methods")
    parser.add_argument(
        "--method", 
        choices=["api", "direct", "queue"], 
        default="direct",
        help="Method to use for proposal generation"
    )
    parser.add_argument(
        "--opportunity-id", 
        default="rwpWgMHaAC",
        help="Opportunity ID to generate proposal for"
    )
    parser.add_argument(
        "--tenant-id", 
        default="8d9e9729-f7bd-44a0-9cf1-777f532a2db2",
        help="Tenant ID"
    )
    parser.add_argument(
        "--client", 
        default="adeptengineeringsolutions",
        help="Client short name"
    )
    parser.add_argument(
        "--volumes", 
        nargs="+", 
        type=int, 
        default=[1],
        help="Volumes to generate (e.g., --volumes 1 2 3)"
    )
    parser.add_argument(
        "--rfp", 
        action="store_true", 
        default=True,
        help="Generate RFP (default: True)"
    )
    parser.add_argument(
        "--rfi", 
        action="store_true",
        help="Generate RFI instead of RFP"
    )
    parser.add_argument(
        "--no-review", 
        action="store_true",
        help="Skip review stage and go directly to formatting"
    )
    
    args = parser.parse_args()
    
    # Determine if RFP or RFI
    is_rfp = not args.rfi
    set_for_review = not args.no_review
    
    print("="*80)
    print("🚀 PROPOSAL GENERATION SCRIPT")
    print("="*80)
    print(f"Method: {args.method}")
    print(f"Opportunity ID: {args.opportunity_id}")
    print(f"Tenant ID: {args.tenant_id}")
    print(f"Client: {args.client}")
    print(f"Type: {'RFP' if is_rfp else 'RFI'}")
    print(f"Volumes: {args.volumes}")
    print(f"Set for Review: {set_for_review}")
    print("="*80)
    
    # Create job instruction
    job_instruction = create_job_instruction(
        opportunity_id=args.opportunity_id,
        tenant_id=args.tenant_id,
        client_short_name=args.client,
        is_rfp=is_rfp,
        volumes=args.volumes,
        set_for_review=set_for_review
    )
    
    print("📋 Job Instruction:")
    print(json.dumps(job_instruction, indent=2))
    print("="*80)
    
    # Initialize generator
    generator = ProposalGenerator()
    
    # Generate proposal using selected method
    if args.method == "api":
        result = await generator.generate_via_api(job_instruction)
    elif args.method == "queue":
        result = await generator.generate_via_queue(job_instruction)
    else:  # direct
        result = await generator.generate_direct(job_instruction)
    
    print("\n" + "="*80)
    print("📊 RESULT:")
    print("="*80)
    print(json.dumps(result, indent=2))
    
    if "error" not in result:
        print("\n✅ Proposal generation initiated successfully!")
        if args.method in ["api", "queue"]:
            print("💡 The proposal will be processed asynchronously.")
            print("💡 Check the database or logs for completion status.")
    else:
        print("\n❌ Proposal generation failed!")


if __name__ == "__main__":
    asyncio.run(main())
