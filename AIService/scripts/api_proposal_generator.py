#!/usr/bin/env python3
"""
API Proposal Generator Script

This script generates a proposal by calling the API endpoint, just like a frontend would.
It uses the /queue/proposal endpoint to create a proposal generation job.

Usage:
    python api_proposal_generator.py
    
Prerequisites:
    - The FastAPI server must be running (python -m uvicorn main:app --reload)
    - pip install httpx (if not already installed)
"""

import asyncio
import json
import httpx
from typing import Dict, Any


async def create_proposal_via_api(
    opportunity_id: str = "rwpWgMHaAC",
    tenant_id: str = "8d9e9729-f7bd-44a0-9cf1-777f532a2db2",
    base_url: str = "http://localhost:8000"
) -> Dict[str, Any]:
    """Create a proposal by calling the API endpoint"""
    
    # Create the job instruction (same as your pipeline.py)
    job_instruction = {
        "opportunityId": opportunity_id,
        "clientShortName": "adeptengineeringsolutions",
        "tenantId": tenant_id,
        "profileId": "2",
        "opportunityType": "custom",
        "sourceDocuments": [],
        "forceRefresh": False,
        "setForReview": True,
        "exportType": 1,
        "proposalRequestType": 1,
        "coverPage": None,
        "trailingPage": None,
        "systemPromptParameters": None,
        "isRFP": True,
        "generatedVolumes": [1],  # Generate only Volume 1 for faster testing
        "aiPersonalityId": "6"
    }
    
    # Prepare the API request payload
    api_payload = {
        "job_instruction": json.dumps(job_instruction),
        "opps_id": opportunity_id,
        "tenant_id": tenant_id,
        "request_type": 1,  # RFP request type
        "job_submitted_by": "api_script_user",
        "next_state": None
    }
    
    print("📋 Job Instruction:")
    print(json.dumps(job_instruction, indent=2))
    print("\n📡 API Payload:")
    print(json.dumps(api_payload, indent=2))
    print("="*80)
    
    async with httpx.AsyncClient(timeout=300.0) as client:
        try:
            print(f"🌐 Calling API endpoint: {base_url}/queue/proposal")
            response = await client.post(
                f"{base_url}/queue/proposal",
                json=api_payload,
                headers={"Content-Type": "application/json"}
            )
            
            print(f"📊 Response Status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ Proposal queue item created successfully!")
                return result
            else:
                print(f"❌ API Error: {response.status_code}")
                print(f"Response: {response.text}")
                return {"error": f"HTTP {response.status_code}: {response.text}"}
                
        except httpx.ConnectError:
            print("❌ Connection Error: Could not connect to the API server.")
            print("💡 Make sure the FastAPI server is running:")
            print("   cd AIService")
            print("   python -m uvicorn main:app --reload")
            return {"error": "Connection failed - server not running"}
            
        except httpx.TimeoutException:
            print("❌ Timeout Error: The request took too long.")
            return {"error": "Request timeout"}
            
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            return {"error": str(e)}


async def check_queue_status(job_id: str, base_url: str = "http://localhost:8000") -> Dict[str, Any]:
    """Check the status of a queued proposal job (if such endpoint exists)"""
    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            # Note: This endpoint might not exist in your current API
            # This is just an example of how you might check status
            response = await client.get(f"{base_url}/queue/proposal/{job_id}")
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"Status check failed: {response.status_code}"}
        except Exception as e:
            return {"error": f"Status check error: {e}"}


async def main():
    """Main function to demonstrate API-based proposal generation"""
    
    print("="*80)
    print("🌐 API PROPOSAL GENERATOR")
    print("="*80)
    print("This script calls the /queue/proposal endpoint to generate a proposal.")
    print("="*80)
    
    # Use the opportunity ID from your selected code
    opportunity_id = "rwpWgMHaAC"
    tenant_id = "8d9e9729-f7bd-44a0-9cf1-777f532a2db2"
    
    print(f"Opportunity ID: {opportunity_id}")
    print(f"Tenant ID: {tenant_id}")
    print("="*80)
    
    # Create proposal via API
    result = await create_proposal_via_api(opportunity_id, tenant_id)
    
    print("\n" + "="*80)
    print("📊 RESULT:")
    print("="*80)
    print(json.dumps(result, indent=2))
    
    if "error" not in result:
        print("\n✅ Proposal generation job created successfully!")
        print("💡 The proposal will be processed asynchronously by the background worker.")
        print("💡 Check the following tables in your database:")
        print("   - customer_proposal_queue (for job status)")
        print("   - proposals_in_review (for generated content if setForReview=True)")
        print("   - proposals_format_queue (for formatted output if setForReview=False)")
        
        # If we got a job_id, we could check status (if endpoint exists)
        if "job_id" in result:
            job_id = result["job_id"]
            print(f"\n🔍 Job ID: {job_id}")
            print("💡 You can use this job_id to track the proposal generation progress.")
    else:
        print("\n❌ Failed to create proposal generation job!")


if __name__ == "__main__":
    asyncio.run(main())
